<?php

namespace App\Factories\Inventory;

use App\Domains\Inventory\Client;
use App\Factories\OrganizationFactory;
use App\Http\Requests\Client\StoreRequest;
use App\Http\Requests\Client\UpdateRequest;
use App\Models\Client as ClientModel;
use App\Services\ASAAS\Factories\AsaasClientFactory;
use Illuminate\Support\Collection;

class ClientFactory
{
    private ?AsaasClientFactory $asaasClientFactory = null;
    private ?OrganizationFactory $organizationFactory = null;

    public function buildFromStoreRequest(StoreRequest $request) : Client {
        return new Client(
            null,
            $request->organization_id ?? null,
            $request->name ?? "",
            $request->phone ?? null,
            $request->whatsapp_from ?? null,
            $request->email ?? null,
            $request->profession ?? null,
            $request->birthdate ?? null,
            $request->cpf ?? null,
            $request->cnpj ?? null,
            $request->service ?? null,
            $request->address ?? null,
            $request->number ?? null,
            $request->neighborhood ?? null,
            $request->cep ?? null,
            $request->complement ?? null,
            $request->civil_state ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromUpdateRequest(UpdateRequest $request) : Client {
        return new Client(
            null,
            null,
            $request->name ?? "",
            $request->phone ?? null,
            $request->whatsapp_from ?? null,
            $request->email ?? null,
            $request->profession ?? null,
            $request->birthdate ?? null,
            $request->cpf ?? null,
            $request->cnpj ?? null,
            $request->service ?? null,
            $request->address ?? null,
            $request->number ?? null,
            $request->neighborhood ?? null,
            $request->cep ?? null,
            $request->complement ?? null,
            $request->civil_state ?? null,
            $request->description ?? null,
        );
    }

    public function buildFromModel(
        ?ClientModel $client, bool $with_asaas = false, bool $with_organization = false
    ) : ?Client {
        if(!$client){
            return null;
        }

        if ($with_asaas) {
            if (!$this->asaasClientFactory) {  $this->asaasClientFactory = new AsaasClientFactory($this); }
            $asaas = $this->asaasClientFactory->buildFromModel($client->asaas, false);
        }
        if ($with_organization) {
            if (!$this->organizationFactory) {  $this->organizationFactory = new OrganizationFactory(); }
            $organization = $this->organizationFactory->buildFromModel($client->organization);
        }

        return new Client(
            $client->id ?? null,
            $client->organization_id ?? null,
            $client->name ?? "",
            $client->phone ?? null,
            $client->whatsapp_from ?? null,
            $client->email ?? null,
            $client->profession ?? null,
            $client->birthdate ?? null,
            $client->cpf ?? null,
            $client->cnpj ?? null,
            $client->service ?? null,
            $client->address ?? null,
            $client->number ?? null,
            $client->neighborhood ?? null,
            $client->cep ?? null,
            $client->complement ?? null,
            $client->civil_state ?? null,
            $client->description ?? null,
            $client->created_at ?? null,
            $client->updated_at ?? null,
            $organization ?? null,
            $asaas ?? null
        );
    }

    public function buildFromModels(?Collection $clients) : ?array {
        if(!$clients){ return []; }

        $clientsArray = [];
        foreach ($clients as $client){
            $clientsArray[] = $this->buildFromModel($client);
        }
        return $clientsArray;
    }

    public function buildFromWhatsAppInfo(
        $messageData,
        int $organizationId,
        string $name,
        string $phoneNumber
    ) : ?Client {
        return new Client(
            id: null,
            organization_id: $organizationId,
            name: $name,
            phone: $phoneNumber,
            whatsapp_from: $phoneNumber, // WhatsApp number
            email: null,
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: "Created from WhatsApp: {$messageData['message_id']}",
            created_at: null,
            updated_at: null,
            organization: null,
            asaas: null
        );
    }
}
