<?php

namespace App\UseCases\ChatBot\Message;

use App\Enums\MessageStatus;
use App\Factories\ChatBot\MessageFactory;
use App\Helpers\DBLog;
use App\Repositories\MessageRepository;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\Factories\WhatsAppMessageFactory;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use Illuminate\Support\Facades\DB;

class SaveMessageFromChatBot
{
    private MessageRepository $messageRepository;
    private MessageFactory $messageFactory;
    private WhatsAppMessageRepository $whatsAppMessageRepository;
    private WhatsAppMessageFactory $whatsAppMessageFactory;

    public function __construct(
        MessageRepository $messageRepository,
        MessageFactory $messageFactory,
        WhatsAppMessageRepository $whatsAppMessageRepository,
        WhatsAppMessageFactory $whatsAppMessageFactory
    ) {
        $this->messageRepository = $messageRepository;
        $this->messageFactory = $messageFactory;
        $this->whatsAppMessageRepository = $whatsAppMessageRepository;
        $this->whatsAppMessageFactory = $whatsAppMessageFactory;
    }

    /**
     * Save a ChatBot message from SendWhatsAppResponse context
     *
     * @param array $stepResult
     * @param WhatsAppConversation $conversation
     * @param array|null $whatsappResponse
     * @return array ['message' => Message, 'whatsapp_message' => WhatsAppMessage|null]
     */
    public function perform(
        array $stepResult,
        WhatsAppConversation $conversation,
        ?array $whatsappResponse = null
    ): array {
        DB::beginTransaction();

        try {
            // Extract data from context
            $messageText = $stepResult['message'] ?? '';
            $organizationId = $conversation->organization_id;
            $conversationId = $conversation->id;
            $stepId = $stepResult['step_id'] ?? $conversation->current_step_id;
            $clientId = $conversation->client_id;

            $status = MessageStatus::is_draft;
            $errorMessage = null;

            if ($whatsappResponse) {
                if (isset($whatsappResponse['messages'][0]['id'])) {
                    $status = MessageStatus::is_sent;
                } elseif (isset($whatsappResponse['error'])) {
                    $status = MessageStatus::is_failed;
                    $errorMessage = $whatsappResponse['error']['message'] ?? 'Unknown WhatsApp API error';
                }
            }

            $messageDomain = $this->messageFactory->buildChatBotMessage(
                organizationId: $organizationId,
                conversationId: $conversationId,
                stepId: $stepId,
                clientId: $clientId,
                messageText: $messageText,
                status: $status
            );

            if ($status === MessageStatus::is_failed) {
                $messageDomain->last_error_message = $errorMessage;
                $messageDomain->is_fail = true;
            }

            $savedMessage = $this->messageRepository->store($messageDomain);

            $savedWhatsAppMessage = null;
            if ($whatsappResponse && isset($whatsappResponse['messages'][0]['id'])) {
                $whatsAppMessageDomain = $this->whatsAppMessageFactory->buildFromMetaApiResponse(
                    $savedMessage->id,
                    $whatsappResponse
                );

                $savedWhatsAppMessage = $this->whatsAppMessageRepository->store($whatsAppMessageDomain);
            }

            DB::commit();

            return [
                'message' => $savedMessage,
                'whatsapp_message' => $savedWhatsAppMessage
            ];
        } catch (\Exception $e) {
            DB::rollBack();

            DBLog::logError(
                "Failed to save message from chatbot",
                "SaveMessageFromChatBot",
                $organizationId ?? null,
                null,
                [
                    'conversation_id' => $conversationId,
                    'step_result' => $stepResult,
                    'whatsapp_response' => $whatsappResponse,
                    'error' => $e->getMessage()
                ]
            );

            throw $e;
        }
    }
}
