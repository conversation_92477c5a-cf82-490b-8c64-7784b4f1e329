<?php

namespace App\Enums;

enum StepType: string
{
    case MESSAGE = 'message';
    case INTERACTIVE = 'interactive';
    case INPUT = 'input';
    case COMMAND = 'command';
    case CONDITION = 'condition';
    case WEBHOOK = 'webhook';
    case DELAY = 'delay';

    /**
     * Get all available step types
     *
     * @return array
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get step type from string value
     *
     * @param string $value
     * @return StepType|null
     */
    public static function fromString(string $value): ?StepType
    {
        return self::tryFrom($value);
    }

    /**
     * Check if step type requires configuration
     *
     * @return bool
     */
    public function requiresConfiguration(): bool
    {
        return match ($this) {
            self::INTERACTIVE, self::INPUT, self::CONDITION, self::WEBHOOK, self::DELAY => true,
            self::MESSAGE, self::COMMAND => false,
        };
    }

    /**
     * Check if step type supports navigation rules
     *
     * @return bool
     */
    public function supportsNavigationRules(): bool
    {
        return match ($this) {
            self::INTERACTIVE, self::CONDITION => true,
            self::MESSAGE, self::INPUT, self::COMMAND, self::WEBHOOK, self::DELAY => false,
        };
    }

    /**
     * Get default configuration schema for step type
     *
     * @return array
     */
    public function getDefaultConfiguration(): array
    {
        return match ($this) {
            self::MESSAGE => [
                'text' => '',
                'media_type' => null,
                'media_url' => null,
            ],
            self::INTERACTIVE => [
                'text' => '',
                'buttons' => [],
                'list_items' => [],
                'interaction_type' => 'buttons', // buttons, list
            ],
            self::INPUT => [
                'prompt' => '',
                'input_type' => 'text', // text, number, email, phone
                'validation_rules' => [],
                'field_mapping' => '', // client.name, client.email, etc
            ],
            self::COMMAND => [
                'command' => '',
                'parameters' => [],
            ],
            self::CONDITION => [
                'conditions' => [],
                'default_path' => null,
            ],
            self::WEBHOOK => [
                'url' => '',
                'method' => 'POST',
                'headers' => [],
                'payload' => [],
            ],
            self::DELAY => [
                'duration_seconds' => 0,
                'message' => null,
            ],
        };
    }

    /**
     * Validate configuration for step type
     *
     * @param array $configuration
     * @return bool
     */
    public function validateConfiguration(array $configuration): bool
    {
        $defaultConfig = $this->getDefaultConfiguration();
        
        return match ($this) {
            self::MESSAGE => isset($configuration['text']),
            self::INTERACTIVE => isset($configuration['text']) && 
                               (isset($configuration['buttons']) || isset($configuration['list_items'])),
            self::INPUT => isset($configuration['prompt']) && isset($configuration['field_mapping']),
            self::COMMAND => isset($configuration['command']),
            self::CONDITION => isset($configuration['conditions']) && is_array($configuration['conditions']),
            self::WEBHOOK => isset($configuration['url']) && filter_var($configuration['url'], FILTER_VALIDATE_URL),
            self::DELAY => isset($configuration['duration_seconds']) && is_numeric($configuration['duration_seconds']),
        };
    }
}
