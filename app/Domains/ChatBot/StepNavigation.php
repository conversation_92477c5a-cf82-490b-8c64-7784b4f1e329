<?php

namespace App\Domains\ChatBot;

use Carbon\Carbon;

class StepNavigation
{
    // Condition types
    public const string BUTTON_CLICK = 'button_click';
    public const string TEXT_MATCH = 'text_match';
    public const string REGEX = 'regex';
    public const string DEFAULT = 'default';

    public ?int $id;
    public ?int $organization_id;
    public ?int $step_id;
    public ?string $condition_type;
    public ?array $condition_data;
    public ?string $target_step_identifier;
    public ?int $priority;
    public ?bool $is_active;
    public ?Carbon $created_at;
    public ?Carbon $updated_at;

    // Related domains
    public ?Step $step;

    public function __construct(
        ?int $id,
        ?int $organization_id,
        ?int $step_id,
        ?string $condition_type,
        ?array $condition_data,
        ?string $target_step_identifier,
        ?int $priority,
        ?bool $is_active,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?Step $step = null,
    ) {
        $this->id = $id;
        $this->organization_id = $organization_id;
        $this->step_id = $step_id;
        $this->condition_type = $condition_type;
        $this->condition_data = $condition_data;
        $this->target_step_identifier = $target_step_identifier;
        $this->priority = $priority ?? 0;
        $this->is_active = $is_active ?? true;
        $this->created_at = $created_at;
        $this->updated_at = $updated_at;
        $this->step = $step;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "organization_id" => $this->organization_id,
            "step_id" => $this->step_id,
            "condition_type" => $this->condition_type,
            "condition_data" => $this->condition_data,
            "target_step_identifier" => $this->target_step_identifier,
            "priority" => $this->priority,
            "is_active" => $this->is_active,
            "created_at" => ($this->created_at ?? Carbon::now())->format("Y-m-d H:i:s"),
            "updated_at" => ($this->updated_at ?? Carbon::now())->format("Y-m-d H:i:s"),
        ];
    }

    public function toStoreArray(): array
    {
        return [
            "organization_id" => $this->organization_id,
            "step_id" => $this->step_id,
            "condition_type" => $this->condition_type,
            "condition_data" => $this->condition_data,
            "target_step_identifier" => $this->target_step_identifier,
            "priority" => $this->priority ?? 0,
            "is_active" => $this->is_active ?? true,
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            "condition_type" => $this->condition_type,
            "condition_data" => $this->condition_data,
            "target_step_identifier" => $this->target_step_identifier,
            "priority" => $this->priority ?? 0,
            "is_active" => $this->is_active ?? true,
        ];
    }

    /**
     * Check if this navigation rule matches the given input
     */
    public function matchesCondition(string $userInput, ?array $context = null): bool
    {
        if (!$this->is_active) {
            return false;
        }

        return match ($this->condition_type) {
            self::BUTTON_CLICK => $this->matchesButtonClick($userInput, $context),
            self::TEXT_MATCH => $this->matchesTextMatch($userInput),
            self::REGEX => $this->matchesRegex($userInput),
            self::DEFAULT => true, // Default condition always matches
            default => false,
        };
    }

    /**
     * Check if button click matches condition
     */
    private function matchesButtonClick(string $userInput, ?array $context = null): bool
    {
        $buttonId = $this->condition_data['button_id'] ?? null;
        $buttonText = $this->condition_data['button_text'] ?? null;

        if (!$buttonId && !$buttonText) {
            return false;
        }

        // Check if user input matches button ID
        if ($buttonId && $userInput === $buttonId) {
            return true;
        }

        // Check if user input matches button text (case-insensitive)
        if ($buttonText && strcasecmp($userInput, $buttonText) === 0) {
            return true;
        }

        // Check context for button information if available
        if ($context && isset($context['button'])) {
            $contextButton = $context['button'];

            if ($buttonId && ($contextButton['id'] ?? null) === $buttonId) {
                return true;
            }

            if ($buttonText && strcasecmp($contextButton['text'] ?? '', $buttonText) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if text matches exactly (case-insensitive)
     */
    private function matchesTextMatch(string $userInput): bool
    {
        $expectedText = $this->condition_data['text'] ?? '';
        $caseSensitive = $this->condition_data['case_sensitive'] ?? false;

        if ($caseSensitive) {
            return $userInput === $expectedText;
        }

        return strcasecmp($userInput, $expectedText) === 0;
    }

    /**
     * Check if text matches regex pattern
     */
    private function matchesRegex(string $userInput): bool
    {
        $pattern = $this->condition_data['pattern'] ?? '';
        $flags = $this->condition_data['flags'] ?? '';

        if (empty($pattern)) {
            return false;
        }

        // Add delimiters if not present
        if (!str_starts_with($pattern, '/')) {
            $pattern = '/' . $pattern . '/' . $flags;
        }

        try {
            return preg_match($pattern, $userInput) === 1;
        } catch (\Exception $e) {
            // Invalid regex pattern
            return false;
        }
    }

    /**
     * Get all available condition types
     */
    public static function getConditionTypes(): array
    {
        return [
            self::BUTTON_CLICK,
            self::TEXT_MATCH,
            self::REGEX,
            self::DEFAULT,
        ];
    }

    /**
     * Validate condition data based on condition type
     */
    public function validateConditionData(): bool
    {
        if (!$this->condition_data) {
            return $this->condition_type === self::DEFAULT;
        }

        return match ($this->condition_type) {
            self::BUTTON_CLICK => $this->validateButtonClickData(),
            self::TEXT_MATCH => $this->validateTextMatchData(),
            self::REGEX => $this->validateRegexData(),
            self::DEFAULT => true,
            default => false,
        };
    }

    /**
     * Validate button click condition data
     */
    private function validateButtonClickData(): bool
    {
        return isset($this->condition_data['button_id']) || isset($this->condition_data['button_text']);
    }

    /**
     * Validate text match condition data
     */
    private function validateTextMatchData(): bool
    {
        return isset($this->condition_data['text']) && !empty($this->condition_data['text']);
    }

    /**
     * Validate regex condition data
     */
    private function validateRegexData(): bool
    {
        $pattern = $this->condition_data['pattern'] ?? '';

        if (empty($pattern)) {
            return false;
        }

        // Test if regex pattern is valid
        try {
            if (!str_starts_with($pattern, '/')) {
                $pattern = '/' . $pattern . '/';
            }
            preg_match($pattern, '');
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Create a button click navigation rule
     */
    public static function createButtonClickRule(
        int $organizationId,
        int $stepId,
        string $buttonId,
        string $targetStepIdentifier,
        int $priority = 0,
        ?string $buttonText = null
    ): self {
        $conditionData = ['button_id' => $buttonId];

        if ($buttonText) {
            $conditionData['button_text'] = $buttonText;
        }

        return new self(
            null,
            $organizationId,
            $stepId,
            self::BUTTON_CLICK,
            $conditionData,
            $targetStepIdentifier,
            $priority,
            true
        );
    }

    /**
     * Create a text match navigation rule
     */
    public static function createTextMatchRule(
        int $organizationId,
        int $stepId,
        string $text,
        string $targetStepIdentifier,
        int $priority = 0,
        bool $caseSensitive = false
    ): self {
        return new self(
            null,
            $organizationId,
            $stepId,
            self::TEXT_MATCH,
            ['text' => $text, 'case_sensitive' => $caseSensitive],
            $targetStepIdentifier,
            $priority,
            true
        );
    }

    /**
     * Create a regex navigation rule
     */
    public static function createRegexRule(
        int $organizationId,
        int $stepId,
        string $pattern,
        string $targetStepIdentifier,
        int $priority = 0,
        string $flags = ''
    ): self {
        return new self(
            null,
            $organizationId,
            $stepId,
            self::REGEX,
            ['pattern' => $pattern, 'flags' => $flags],
            $targetStepIdentifier,
            $priority,
            true
        );
    }

    /**
     * Create a default navigation rule (fallback)
     */
    public static function createDefaultRule(
        int $organizationId,
        int $stepId,
        string $targetStepIdentifier,
        int $priority = 999 // Default rules should have low priority
    ): self {
        return new self(
            null,
            $organizationId,
            $stepId,
            self::DEFAULT,
            null,
            $targetStepIdentifier,
            $priority,
            true
        );
    }
}
