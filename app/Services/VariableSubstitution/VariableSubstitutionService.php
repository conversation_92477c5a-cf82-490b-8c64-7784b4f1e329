<?php

namespace App\Services\VariableSubstitution;

use App\Domains\Inventory\Client;
use App\Domains\ChatBot\Campaign;
use App\Domains\ChatBot\Template;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Domains\User;
use Exception;

class VariableSubstitutionService
{
    /**
     * Available models for variable substitution
     */
    private const AVAILABLE_MODELS = [
        'client' => Client::class,
        'campaign' => Campaign::class,
        'template' => Template::class,
        'phone_number' => PhoneNumber::class,
        'organization' => Organization::class,
        'user' => User::class,
    ];

    /**
     * Replace variables in text using {{model.variable}} pattern
     *
     * @param string $text
     * @param array $availableModels - Array of model instances keyed by model name
     * @return string
     */
    public function substitute(string $text, array $availableModels = []): string
    {
        // Find all variable patterns {{model.variable}}
        $pattern = '/\{\{([a-zA-Z_]+)\.([a-zA-Z_]+)\}\}/';

        return preg_replace_callback($pattern, function ($matches) use ($availableModels) {
            $modelName = $matches[1];
            $variableName = $matches[2];

            return $this->getVariableValue($modelName, $variableName, $availableModels);
        }, $text);
    }

    /**
     * Get variable value from model
     *
     * @param string $modelName
     * @param string $variableName
     * @param array $availableModels
     * @return string
     */
    private function getVariableValue(string $modelName, string $variableName, array $availableModels): string
    {
        // Check if model is available in the provided models
        if (!isset($availableModels[$modelName])) {
            return "{{{$modelName}.{$variableName}}}"; // Return original if model not available
        }

        $model = $availableModels[$modelName];

        if (!$model) {
            return "{{{$modelName}.{$variableName}}}"; // Return original if model is null
        }

        // Get value from model
        $value = $this->extractValueFromModel($model, $variableName);

        return $value ?? "{{{$modelName}.{$variableName}}}"; // Return original if value not found
    }

    /**
     * Extract value from model using different methods
     *
     * @param mixed $model
     * @param string $variableName
     * @return string|null
     */
    private function extractValueFromModel($model, string $variableName): ?string
    {
        // Method 1: Direct property access
        if (is_object($model) && property_exists($model, $variableName)) {
            $value = $model->$variableName;
            return $this->formatValue($value);
        }

        // Method 2: Array access (if model has toArray method)
        if (is_object($model) && method_exists($model, 'toArray')) {
            $array = $model->toArray();
            if (isset($array[$variableName])) {
                return $this->formatValue($array[$variableName]);
            }
        }

        // Method 3: Getter method
        $getterMethod = 'get' . ucfirst($variableName);
        if (is_object($model) && method_exists($model, $getterMethod)) {
            $value = $model->$getterMethod();
            return $this->formatValue($value);
        }

        // Method 4: Array access if model is array
        if (is_array($model) && isset($model[$variableName])) {
            return $this->formatValue($model[$variableName]);
        }

        return null;
    }

    /**
     * Format value for display
     *
     * @param mixed $value
     * @return string
     */
    private function formatValue($value): string
    {
        if ($value === null) {
            return '';
        }

        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }

        if (is_array($value)) {
            return json_encode($value);
        }

        if (is_object($value)) {
            // Handle Carbon dates
            if (method_exists($value, 'format')) {
                return $value->format('Y-m-d H:i:s');
            }

            // Handle other objects
            if (method_exists($value, '__toString')) {
                return (string) $value;
            }

            return json_encode($value);
        }

        return (string) $value;
    }

    /**
     * Get all variables found in text
     *
     * @param string $text
     * @return array
     */
    public function getVariablesFromText(string $text): array
    {
        $pattern = '/\{\{([a-zA-Z_]+)\.([a-zA-Z_]+)\}\}/';
        preg_match_all($pattern, $text, $matches, PREG_SET_ORDER);

        $variables = [];
        foreach ($matches as $match) {
            $variables[] = [
                'full' => $match[0],
                'model' => $match[1],
                'variable' => $match[2]
            ];
        }

        return $variables;
    }

    /**
     * Validate if all variables in text can be resolved
     *
     * @param string $text
     * @param array $availableModels
     * @return array
     */
    public function validateVariables(string $text, array $availableModels = []): array
    {
        $variables = $this->getVariablesFromText($text);
        $errors = [];

        foreach ($variables as $variable) {
            $modelName = $variable['model'];
            $variableName = $variable['variable'];

            // Check if model is available
            if (!isset($availableModels[$modelName])) {
                $errors[] = "Model '{$modelName}' is not available";
                continue;
            }

            // Check if model is valid
            if (!$availableModels[$modelName]) {
                $errors[] = "Model '{$modelName}' is null";
                continue;
            }

            // Check if variable exists in model
            $value = $this->extractValueFromModel($availableModels[$modelName], $variableName);
            if ($value === null) {
                $errors[] = "Variable '{$variableName}' not found in model '{$modelName}'";
            }
        }

        return $errors;
    }

    /**
     * Get available model names
     *
     * @return array
     */
    public function getAvailableModelNames(): array
    {
        return array_keys(self::AVAILABLE_MODELS);
    }

    /**
     * Check if model name is valid
     *
     * @param string $modelName
     * @return bool
     */
    public function isValidModelName(string $modelName): bool
    {
        return isset(self::AVAILABLE_MODELS[$modelName]);
    }
}
