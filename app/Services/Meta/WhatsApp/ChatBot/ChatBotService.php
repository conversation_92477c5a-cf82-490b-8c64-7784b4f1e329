<?php

namespace App\Services\Meta\WhatsApp\ChatBot;

use App\Domains\ChatBot\PhoneNumber;
use App\Helpers\DBLog;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Client\FindOrCreateClient;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ExtractMessageData;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\FindOrCreateConversation;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\GetFlowByPhoneNumber;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\ProcessFlowStep;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\SendWhatsAppResponse;
use Exception;

class ChatBotService
{
    protected ProcessFlowStep $processFlowStep;
    protected FindOrCreateClient $findOrCreateClient;
    protected FindOrCreateConversation $findOrCreateConversation;
    protected SendWhatsAppResponse $sendWhatsAppResponse;
    protected ExtractMessageData $extractMessageData;
    protected GetFlowByPhoneNumber $getFlowByPhoneNumber;

    public function __construct(
        ProcessFlowStep $processFlowStep,
        FindOrCreateClient $findOrCreateClient,
        FindOrCreateConversation $findOrCreateConversation,
        SendWhatsAppResponse $sendWhatsAppResponse,
        ExtractMessageData $extractMessageData,
        GetFlowByPhoneNumber $getFlowByPhoneNumber
    ) {
        $this->processFlowStep = $processFlowStep;
        $this->findOrCreateClient = $findOrCreateClient;
        $this->findOrCreateConversation = $findOrCreateConversation;
        $this->sendWhatsAppResponse = $sendWhatsAppResponse;
        $this->extractMessageData = $extractMessageData;
        $this->getFlowByPhoneNumber = $getFlowByPhoneNumber;
    }

    /**
     * Main entry point for processing WhatsApp webhook messages
     *
     * @param array $webhookData
     * @param PhoneNumber $phoneNumber
     * @return array
     * @throws Exception
     */
    public function processWebhook(array $webhookData, PhoneNumber $phoneNumber): array
    {
        try {

            DBLog::log(
                "ChatBotService::processWebhook - Processing webhook",
                "WhatsApp::ChatBotService",
                null,
                null,
                ['webhook_data' => $webhookData]
            );

            $messageData = $this->extractMessageData->perform($webhookData);

            $flow = $this->getFlowByPhoneNumber->perform($phoneNumber);

            $client = $this->findOrCreateClient->perform($messageData, $phoneNumber->organization);

            $conversation = $this->findOrCreateConversation->perform($messageData, $client, $phoneNumber);

            // Process current flow step
            $stepResult = $this->processFlowStep->perform($conversation, $messageData, $flow);

            $response = $this->sendWhatsAppResponse->perform($stepResult, $conversation);

            return [
                'success' => true,
                'conversation_id' => $conversation->id,
                'client_id' => $client->id,
                'step_result' => $stepResult,
                'response' => $response
            ];

        } catch (Exception $e) {
            DBLog::logError(
                "ChatBotService::processWebhook - Error processing webhook: " . $e->getMessage(),
                "WhatsApp::ChatBotService",
                null,
                null,
                ['webhook_data' => $webhookData, 'error' => $e->getMessage()]
            );

            throw $e;
        }
    }
}
