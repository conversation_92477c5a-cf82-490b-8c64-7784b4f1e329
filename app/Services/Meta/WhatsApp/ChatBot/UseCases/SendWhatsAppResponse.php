<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Helpers\DBLog;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppConversationRepository;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Conversation\FinishConversation;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Conversation\MoveConversationToNextStep;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\SendMessages\SendMessage;
use App\UseCases\ChatBot\Message\SaveMessageFromChatBot;
use Exception;

class SendWhatsAppResponse
{
    protected WhatsAppConversationRepository $conversationRepository;

    private const array SEND_MESSAGE_ACTIONS = [
        'send_message',
        'show_options',
        'request_input',
        'input_invalid',
        'input_processed',
        'default_processing',
    ];

    public function __construct(
        WhatsAppConversationRepository $conversationRepository
    ) {
        $this->conversationRepository = $conversationRepository;
    }

    /**
     * Send WhatsApp response based on step result
     *
     * @param array $stepResult
     * @param WhatsAppConversation $conversation
     * @return array
     * @throws Exception
     */
    public function perform(array $stepResult, WhatsAppConversation $conversation): array
    {
        try {
            if ($stepResult['finish_conversation'] ?? false) {
                /** @var FinishConversation $finishConversation */
                $finishConversation = app()->make(FinishConversation::class);
                $finishConversation->perform($conversation, $stepResult);
            }

            if ($stepResult['move_to_next'] ?? false) {
                /** @var MoveConversationToNextStep $moveUseCase */
                $moveUseCase = app()->make(MoveConversationToNextStep::class);
                $moveUseCase->perform($conversation, $stepResult);
            }

            $response = null;

            $shouldSendMessage = in_array(
                $stepResult['action'] ?? '', self::SEND_MESSAGE_ACTIONS
            ) && !empty($stepResult['message']);

            if ($shouldSendMessage) {
                /** @var SendMessage $useCase */
                $useCase = app()->make(SendMessage::class);
                $response = $useCase->perform($stepResult, $conversation);

                /** @var SaveMessageFromChatBot $saveMessageUseCase */
                $saveMessageUseCase = app()->make(SaveMessageFromChatBot::class);
                $savedMessages = $saveMessageUseCase->perform(
                    $stepResult,
                    $conversation,
                    $response
                );

                DBLog::log(
                    "ChatBot message saved",
                    "WhatsApp::SendWhatsAppResponse",
                    $conversation->organization_id ?? null,
                    null,
                    [
                        'conversation_id' => $conversation->id,
                        'message_id' => $savedMessages['message']->id,
                        'whatsapp_message_id' => $savedMessages['whatsapp_message']?->id,
                        'step_result' => $stepResult
                    ]
                );
            }

            $sendInformation = [
                'sent' => $response !== null,
                'response' => $response,
                'conversation_updated' => $stepResult['move_to_next'] ?? false,
                'conversation_finished' => $stepResult['finish_conversation'] ?? false,
                'step_result' => $stepResult,
            ];

            DBLog::log(
                "WhatsApp response sent",
                "WhatsApp::SendWhatsAppResponse",
                $conversation->organization_id ?? null,
                null,
                [
                    'conversation_id' => $conversation->id,
                    'step_result' => $stepResult,
                    'response' => $response
                ]
            );

            return $sendInformation;

        } catch (Exception $e) {
            DBLog::logError(
                "Failed to send WhatsApp response",
                "WhatsApp::SendWhatsAppResponse",
                $conversation->organization_id ?? null,
                null,
                [
                    'conversation_id' => $conversation->id,
                    'step_result' => $stepResult,
                    'error' => $e->getMessage()
                ]
            );

            throw $e;
        }
    }
}
