<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases\Conversation;

use App\Helpers\DBLog;
use App\Repositories\StepRepository;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppConversationRepository;
use App\Services\Meta\WhatsApp\ChatBot\Services\ChatBotMessageService;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Conversation\FinishConversation;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\SendMessages\SendMessage;
use Exception;

class MoveConversationToNextStep
{
    protected WhatsAppConversationRepository $conversationRepository;
    protected StepRepository $stepRepository;

    public function __construct(
        WhatsAppConversationRepository $conversationRepository,
        StepRepository $stepRepository
    ) {
        $this->conversationRepository = $conversationRepository;
        $this->stepRepository = $stepRepository;
    }

    /**
     * Move conversation to next step
     */
    public function perform(WhatsAppConversation $conversation, array $stepResult): void
    {
        try {
            $nextStepId = $stepResult['next_step'] ?? null;

            if ($nextStepId) {
                $conversation->current_step_id = is_string($nextStepId) ? (int) $nextStepId : $nextStepId;
                $conversation->current_step = $this->stepRepository->fetchById($nextStepId);
            } else {
                $conversation->finish();
            }

            $this->conversationRepository->save($conversation);
        } catch (Exception $e) {
            DBLog::logError(
                "Failed to move conversation to next step",
                "WhatsApp::MoveConversationToNextStep",
                $conversation->organization_id ?? null,
                null,
                [
                    'conversation_id' => $conversation->id,
                    'step_result' => $stepResult,
                    'error' => $e->getMessage()
                ]
            );

            throw $e;
        }
    }
}
