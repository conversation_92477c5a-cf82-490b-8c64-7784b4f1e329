<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Domains\ChatBot\Flow;
use App\Helpers\DBLog;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppInteractionRepository;
use App\Services\Meta\WhatsApp\ChatBot\Factories\WhatsAppInteractionFactory;
use App\Services\Meta\WhatsApp\ChatBot\Services\ConditionalNavigationService;
use App\Services\Meta\WhatsApp\ChatBot\Services\ErrorHandlerService;
use App\Services\Meta\WhatsApp\ChatBot\Processors\StepProcessorFactory;
use App\Domains\ChatBot\Step;
use Exception;

class ProcessFlowStep
{
    protected WhatsAppInteractionRepository $interactionRepository;
    protected WhatsAppInteractionFactory $interactionFactory;
    protected ConditionalNavigationService $conditionalNavigationService;
    protected ErrorHandlerService $errorHandlerService;
    protected StepProcessorFactory $stepProcessorFactory;

    public function __construct(
        WhatsAppInteractionRepository $interactionRepository,
        WhatsAppInteractionFactory $interactionFactory,
        ConditionalNavigationService $conditionalNavigationService,
        ErrorHandlerService $errorHandlerService,
        StepProcessorFactory $stepProcessorFactory
    ) {
        $this->interactionRepository = $interactionRepository;
        $this->interactionFactory = $interactionFactory;
        $this->conditionalNavigationService = $conditionalNavigationService;
        $this->errorHandlerService = $errorHandlerService;
        $this->stepProcessorFactory = $stepProcessorFactory;
    }

    /**
     * Process current flow step and determine next action
     *
     * @param WhatsAppConversation $conversation
     * @param array $messageData
     * @return array
     * @throws Exception
     */
    public function perform(WhatsAppConversation $conversation, array $messageData, Flow $flow): array
    {
        $interaction = null;
        $currentStep = null;

        try {
            $interaction = $this->interactionRepository->save(
                $this->interactionFactory->buildFromWebhookData($messageData, $conversation)
            );

            $currentStep = $conversation->current_step;
            if (!$currentStep) {
                throw new Exception('No current step found for conversation');
            }

            $stepResult = $this->processStepByType($currentStep, $interaction, $conversation);
            DBLog::log(
                "Step processed",
                "WhatsApp::ProcessFlowStep",
                $conversation->organization_id ?? null,
                null,
                ['step_result' => $stepResult]
            );

            $stepResult = $this->applyConditionalNavigation($stepResult, $currentStep, $interaction);
            DBLog::log(
                "Conditional navigation applied",
                "WhatsApp::ProcessFlowStep",
                $conversation->organization_id ?? null,
                null,
                ['step_result' => $stepResult]
            );

            $interaction->result = json_encode($stepResult);
            $this->interactionRepository->save($interaction);

            return $stepResult;

        } catch (\Exception $e) {
            $errorResult = $this->errorHandlerService->handleError(
                $e,
                null,
                $conversation,
                $interaction,
                $currentStep
            );

            return [
                'type' => 'error',
                'action' => $errorResult['action'],
                'message' => $errorResult['message'],
                'error_strategy' => $errorResult['strategy'],
                'move_to_next' => !($errorResult['stay_on_current_step'] ?? false),
                'next_step' => $errorResult['fallback_step'] ?? ($currentStep?->next_step ?? null),
                'error_handled' => true,
                'original_error' => $e->getMessage(),
            ];
        }
    }


    /**
     * Process step based on its type using Strategy Pattern
     */
    protected function processStepByType(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        try {
            $processor = $this->stepProcessorFactory->getProcessor($step);
            DBLog::log(
                "Step processor found",
                "WhatsApp::ProcessFlowStep",
                $conversation->organization_id ?? null,
                null,
                ['step_id' => $step->id, 'step_type' => $step->step_type?->value ?? 'unknown']
            );
            return $processor->process($step, $interaction, $conversation);
        } catch (\InvalidArgumentException $e) {
            \Log::warning('No processor found for step type, using default processing', [
                'step_id' => $step->id,
                'step_type' => $step->step_type?->value ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            return $this->processDefaultStep($step);
        }
    }

    /**
     * Process default step when no specific processor is found
     *
     * This is a fallback method that handles steps when:
     * - Step type is not recognized by StepProcessorFactory
     * - Step type is null or invalid
     * - StepProcessor throws an exception
     *
     * Used in scenarios like:
     * - Legacy steps without proper step_type
     * - Corrupted step data
     * - Missing processor implementations
     */
    protected function processDefaultStep(Step $step): array
    {
        return [
            'type' => 'default',
            'step_id' => $step->id,
            'action' => 'default_processing',
            'message' => $this->getStepMessage($step),
            'next_step' => $step->next_step,
            'move_to_next' => true,
        ];
    }

    /**
     * Get message content for step
     */
    protected function getStepMessage(Step $step): string
    {
        // First try to get message from JSON configuration
        if ($step->json) {
            $jsonData = json_decode($step->json, true);
            if (is_array($jsonData) && isset($jsonData['message'])) {
                return $jsonData['message'];
            }
        }

        // Fallback to step name
        return $step->step ?? 'Step message not configured';
    }



    /**
     * Apply conditional navigation to step result
     *
     * This is the CORE method that handles navigation between steps based on user interaction.
     * It's called after a step is processed to determine where to go next.
     *
     * CRITICAL for flow functionality - this method:
     * 1. Checks if step should move to next (move_to_next = true)
     * 2. Determines if step has conditional navigation rules
     * 3. Finds the correct next step based on user interaction
     * 4. Updates step result with new navigation target
     *
     * Used in scenarios like:
     * - Pizzaria: User clicks "🍕 Pequena" → goes to "choose_pizza_flavor"
     * - Pizzaria: User clicks "🍅 Margherita" → goes to "collect_address"
     * - Any CONDITION step with navigation_rules
     * - Button clicks that should navigate to specific steps
     *
     * @param array $stepResult Result from step processor
     * @param Step $currentStep Current step being processed
     * @param WhatsAppInteraction $interaction User's interaction (button click, text, etc)
     * @return array Updated step result with navigation applied
     */
    protected function applyConditionalNavigation(
        array $stepResult,
        Step $currentStep,
        WhatsAppInteraction $interaction
    ): array {
        // Only apply conditional navigation if we're moving to next step
        if (!($stepResult['move_to_next'] ?? false)) {
            DBLog::log(
                "Conditional navigation skipped - not moving to next step",
                "WhatsApp::ProcessFlowStep",
                $interaction->conversation->organization_id ?? null,
                null,
                ['step_result' => $stepResult]
            );
            return $stepResult;
        }

        // Check if current step has conditional navigation
        if (!$this->conditionalNavigationService->hasConditionalNavigation($currentStep)) {
            DBLog::log(
                "Conditional navigation skipped - no conditional navigation rules",
                "WhatsApp::ProcessFlowStep",
                $interaction->conversation->organization_id ?? null,
                null,
                ['step_id' => $currentStep->id, 'step_identifier' => $currentStep->step]
            );
            return $stepResult;
        }

        // Get the next step based on user interaction
        $conditionalNextStep = $this->conditionalNavigationService->getNextStepForInteraction($currentStep, $interaction);

        if ($conditionalNextStep) {
            // Override the next step with conditional target
            $stepResult['next_step'] = $conditionalNextStep->id;
            $stepResult['conditional_navigation'] = true;
            $stepResult['target_step_identifier'] = $conditionalNextStep->step;

            DBLog::log(
                "Conditional navigation applied successfully",
                "WhatsApp::ProcessFlowStep",
                $interaction->conversation->organization_id ?? null,
                null,
                [
                    'from_step' => $currentStep->step,
                    'to_step' => $conditionalNextStep->step,
                    'button_id' => $interaction->getButtonId(),
                    'interaction_type' => $interaction->whatsapp_message_type,
                    'step_result' => $stepResult
                ]
            );
        } else {
            DBLog::log(
                "Conditional navigation failed - no target step found",
                "WhatsApp::ProcessFlowStep",
                $interaction->conversation->organization_id ?? null,
                null,
                [
                    'current_step' => $currentStep->step,
                    'button_id' => $interaction->getButtonId(),
                    'interaction_type' => $interaction->whatsapp_message_type
                ]
            );
        }

        return $stepResult;
    }
}
