<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases\SendMessages;

use App\Helpers\DBLog;
use App\Repositories\ClientRepository;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppConversationRepository;
use App\Services\Meta\WhatsApp\ChatBot\Services\ChatBotMessageService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;

class SendMessage
{
    protected WhatsAppConversationRepository $conversationRepository;
    protected ClientRepository $clientRepository;

    public function __construct(
        WhatsAppConversationRepository $conversationRepository,
        ClientRepository $clientRepository
    ) {
        $this->conversationRepository = $conversationRepository;
        $this->clientRepository = $clientRepository;
    }

    /**
     * Send message to WhatsApp
     * @param array $stepResult
     * @param WhatsAppConversation $conversation
     * @return array
     * @throws BindingResolutionException
     */
    public function perform(array $stepResult, WhatsAppConversation $conversation): array
    {
        try {
            $messageText = $stepResult['message'] ?? '';

            if (empty($messageText)) {
                throw new Exception('No message content to send');
            }

            $phoneNumber = $conversation->phone_number;
            if (!$phoneNumber) {
                throw new Exception('No phone number found for conversation');
            }

            $messageService = new ChatBotMessageService($phoneNumber);

            if (!$conversation->client_id) {
                throw new Exception('No client found for conversation');
            }

            $client = $this->clientRepository->fetchById($conversation->client_id);
            if (!$client) {
                throw new Exception('Client not found');
            }

            /** @var SendMessageByType $useCase */
            $useCase = app()->make(SendMessageByType::class);
            return $useCase->perform($stepResult, $messageService, $client, $conversation);
        } catch (Exception $e) {
            DBLog::logError(
                "Failed to send message",
                "WhatsApp::ChatBot::SendMessage",
                $conversation->organization_id ?? null,
                null,
                [
                    'conversation_id' => $conversation->id,
                    'step_result' => $stepResult,
                    'error' => $e->getMessage()
                ]
            );

            throw $e;
        }
    }
}
