<?php

namespace App\Services\Meta\WhatsApp\ChatBot\UseCases;

use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Repositories\WhatsAppConversationRepository;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\CreateNewFromFindOrCreate;
use App\Domains\Inventory\Client;
use App\Domains\ChatBot\PhoneNumber;
use App\Repositories\PhoneNumberRepository;
use App\Helpers\DBLog;
use Exception;

class FindOrCreateConversation
{
    protected WhatsAppConversationRepository $conversationRepository;
    protected CreateNewFromFindOrCreate $createNewFromFindOrCreate;
    protected PhoneNumberRepository $phoneNumberRepository;

    public function __construct(
        WhatsAppConversationRepository $conversationRepository,
        CreateNewFromFindOrCreate $createNewFromFindOrCreate,
        PhoneNumberRepository $phoneNumberRepository
    ) {
        $this->conversationRepository = $conversationRepository;
        $this->createNewFromFindOrCreate = $createNewFromFindOrCreate;
        $this->phoneNumberRepository = $phoneNumberRepository;
    }

    /**
     * Find existing conversation or create new one
     *
     * @param array $messageData
     * @param Client $client
     * @param PhoneNumber $phoneNumber
     * @return WhatsAppConversation
     * @throws Exception
     */
    public function perform(array $messageData, Client $client, PhoneNumber $phoneNumber): WhatsAppConversation
    {
        try {
            DBLog::log(
                "FindOrCreateConversation::perform - Starting conversation lookup",
                "WhatsApp::ChatBot::FindOrCreateConversation",
                $phoneNumber->organization_id,
                null,
                [
                    'client_id' => $client->id,
                    'phone_number_id' => $phoneNumber->id,
                    'phone_number' => $phoneNumber->phone_number,
                    'message_data_keys' => array_keys($messageData)
                ]
            );

            $existingConversation = $this->conversationRepository->findActiveConversation($client, $phoneNumber);

            if ($existingConversation) {
                DBLog::log(
                    "FindOrCreateConversation::perform - Found existing active conversation",
                    "WhatsApp::ChatBot::FindOrCreateConversation",
                    $phoneNumber->organization_id,
                    null,
                    [
                        'conversation_id' => $existingConversation->id,
                        'client_id' => $client->id,
                        'phone_number_id' => $phoneNumber->id,
                        'current_step_id' => $existingConversation->current_step_id
                    ]
                );
                return $existingConversation;
            }

            DBLog::log(
                "FindOrCreateConversation::perform - No existing conversation found, creating new one",
                "WhatsApp::ChatBot::FindOrCreateConversation",
                $phoneNumber->organization_id,
                null,
                [
                    'client_id' => $client->id,
                    'phone_number_id' => $phoneNumber->id,
                    'phone_number' => $phoneNumber->phone_number
                ]
            );

            return $this->createNewFromFindOrCreate->perform($messageData, $client, $phoneNumber);

        } catch (Exception $e) {
            DBLog::logError(
                "FindOrCreateConversation::perform - Error finding or creating conversation: " . $e->getMessage(),
                "WhatsApp::ChatBot::FindOrCreateConversation",
                $phoneNumber->organization_id ?? null,
                null,
                [
                    'client_id' => $client->id ?? null,
                    'phone_number_id' => $phoneNumber->id ?? null,
                    'phone_number' => $phoneNumber->phone_number ?? null,
                    'organization_id' => $phoneNumber->organization_id ?? null,
                    'error_message' => $e->getMessage(),
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine(),
                    'message_data_keys' => array_keys($messageData ?? [])
                ]
            );

            throw $e;
        }
    }
}
