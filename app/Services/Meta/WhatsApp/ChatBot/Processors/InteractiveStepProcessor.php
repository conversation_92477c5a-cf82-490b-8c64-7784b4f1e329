<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Helpers\DBLog;
use App\Repositories\StepRepository;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Enums\StepType;

/**
 * Processor for INTERACTIVE type steps
 *
 * Handles interactive steps that present buttons or lists to users
 * and process their selections. Supports conditional navigation
 * based on user choices.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
class InteractiveStepProcessor implements StepProcessorInterface
{
    /**
     * Process an interactive step
     *
     * Interactive steps can either show options to the user or process
     * their selection depending on whether they've already interacted.
     *
     * @param Step $step
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     */
    public function process(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        // Check if user provided a valid interaction
        $selectedOption = $this->getSelectedOption($interaction);
        DBLog::log(
            "Selected option: " . $selectedOption,
            "WhatsApp::InteractiveStepProcessor",
            $conversation->organization_id ?? null,
            null,
            ['step_id' => $step->id, 'interaction_id' => $interaction->id, "selectedOption" => $selectedOption, "interaction" => $interaction->toArray()]
        );
        if ($selectedOption) {
            // User made a selection, process it
            return [
                'type' => 'interactive',
                'step_id' => $step->id,
                'action' => 'process_selection',
                'selection' => $selectedOption,
                'next_step' => $this->getNextStepForSelection($step, $selectedOption),
                'move_to_next' => true,
            ];
        } else {
            // Show interactive options
            return [
                'type' => 'interactive',
                'step_id' => $step->id,
                'action' => 'show_options',
                'message' => $this->getStepMessage($step),
                'options' => $this->getStepOptions($step),
                'move_to_next' => false,
            ];
        }
    }

    /**
     * Check if this processor can handle the given step
     *
     * @param Step $step
     * @return bool
     */
    public function canProcess(Step $step): bool
    {
        // Ensure step_type is set from legacy fields if needed
        $step->setStepTypeFromLegacyFields();

        return $step->step_type === StepType::INTERACTIVE;
    }

    /**
     * Get supported step types
     *
     * @return array
     */
    public function getSupportedStepTypes(): array
    {
        return [StepType::INTERACTIVE->value];
    }

    /**
     * Get message content for step
     *
     * @param Step $step
     * @return string
     */
    protected function getStepMessage(Step $step): string
    {
        // First try to get message from JSON configuration
        if ($step->json) {
            $jsonData = json_decode($step->json, true);
            if (is_array($jsonData) && isset($jsonData['message'])) {
                return $jsonData['message'];
            }
            // Also check for 'text' field as alternative
            if (is_array($jsonData) && isset($jsonData['text'])) {
                return $jsonData['text'];
            }
        }

        // Second try to get message from component text
        if ($step->component && $step->component->text) {
            return $step->component->text;
        }

        // Fallback to step name
        return $step->step ?? 'Please select an option:';
    }

    /**
     * Get interactive options for step
     *
     * @param Step $step
     * @return array
     */
    protected function getStepOptions(Step $step): array
    {
        $options = [];

        // Get options from step component (buttons)
        if ($step->component && $step->component->buttons && !empty($step->component->buttons)) {
            foreach ($step->component->buttons as $button) {
                $options[] = [
                    'id' => $button->callback_data_array['button_id'] ?? $button->id,
                    'text' => $button->text,
                    'type' => 'button',
                ];
            }
        }

        // Get options from JSON configuration if no components
        if (empty($options) && $step->json) {
            $jsonData = json_decode($step->json, true);
            if (is_array($jsonData)) {
                // Check for buttons array
                if (isset($jsonData['buttons']) && is_array($jsonData['buttons'])) {
                    foreach ($jsonData['buttons'] as $button) {
                        $options[] = [
                            'id' => $button['id'] ?? $button['text'],
                            'text' => $button['text'],
                            'type' => 'button',
                        ];
                    }
                }
                // Check for list_items array
                if (isset($jsonData['list_items']) && is_array($jsonData['list_items'])) {
                    foreach ($jsonData['list_items'] as $item) {
                        $options[] = [
                            'id' => $item['id'] ?? $item['text'],
                            'text' => $item['text'],
                            'type' => 'list_item',
                        ];
                    }
                }
            }
        }

        return $options;
    }

    /**
     * Get selected option from interaction
     *
     * @param WhatsAppInteraction $interaction
     * @return string|null
     */
    protected function getSelectedOption(WhatsAppInteraction $interaction): ?string
    {
        if ($interaction->isButtonInteraction()) {
            return $interaction->getButtonId();
        }

        if ($interaction->isListInteraction()) {
            return $interaction->getListSelectionId();
        }

        return null;
    }

    /**
     * Get next step based on user selection
     *
     * @param Step $step
     * @param string $selection
     * @return int|null
     */
    protected function getNextStepForSelection(Step $step, string $selection): ?int
    {
        // Simple: convert position to step_id
        if ($step->next_step !== null) {
            $stepRepository = app()->make(StepRepository::class);
            $nextStep = $stepRepository->findByPositionInFlow($step->next_step, $step->flow_id);
            return $nextStep?->id;
        }

        return null;
    }
}
