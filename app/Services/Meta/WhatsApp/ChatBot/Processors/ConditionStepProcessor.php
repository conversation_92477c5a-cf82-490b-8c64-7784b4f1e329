<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppConversation;
use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Services\VariableSubstitution\VariableSubstitutionService;
use App\Enums\StepType;

/**
 * Processor for CONDITION type steps
 *
 * Handles conditional steps that evaluate conditions and determine
 * the next step in the flow based on conversation state, user data,
 * or other criteria. This enables branching logic in conversations.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
class ConditionStepProcessor implements StepProcessorInterface
{
    protected VariableSubstitutionService $variableSubstitutionService;

    public function __construct(VariableSubstitutionService $variableSubstitutionService)
    {
        $this->variableSubstitutionService = $variableSubstitutionService;
    }
    /**
     * Process a condition step
     *
     * FIXED: Condition steps now properly handle button clicks and navigation_rules
     * This is critical for pizzaria flow to work correctly.
     *
     * @param Step $step
     * @param WhatsAppInteraction $interaction
     * @param WhatsAppConversation $conversation
     * @return array
     */
    public function process(
        Step $step,
        WhatsAppInteraction $interaction,
        WhatsAppConversation $conversation
    ): array {
        // For CONDITION steps, we need to show the message/buttons and let conditional navigation handle the rest
        $message = $this->getStepMessage($step, $conversation);

        return [
            'type' => 'condition',
            'step_id' => $step->id,
            'action' => 'show_condition_message',
            'message' => $message,
            'next_step' => $step->next_step, // Default fallback
            'move_to_next' => true, // CRITICAL: This allows conditional navigation to work
            'has_buttons' => $this->stepHasButtons($step),
            'has_navigation_rules' => !empty($step->navigation_rules),
        ];
    }

    /**
     * Check if this processor can handle the given step
     *
     * @param Step $step
     * @return bool
     */
    public function canProcess(Step $step): bool
    {
        // Ensure step_type is set from legacy fields if needed
        $step->setStepTypeFromLegacyFields();

        return $step->step_type === StepType::CONDITION;
    }

    /**
     * Get supported step types
     *
     * @return array
     */
    public function getSupportedStepTypes(): array
    {
        return [StepType::CONDITION->value];
    }

    /**
     * Get step message with variable substitution
     *
     * @param Step $step
     * @param WhatsAppConversation $conversation
     * @return string|null
     */
    protected function getStepMessage(Step $step, WhatsAppConversation $conversation): ?string
    {
        // Try to get message from component first
        if ($step->component && $step->component->text) {
            $message = $step->component->text;
        } else {
            // Fallback to JSON data
            $jsonData = json_decode($step->json ?? '{}', true);
            $message = $jsonData['message'] ?? null;
        }

        if (!$message) {
            return null;
        }

        // Apply variable substitution
        $availableModels = [
            'client' => $conversation->client ?? null,
            'conversation' => $conversation,
            'step' => $step,
        ];

        return $this->variableSubstitutionService->substitute($message, $availableModels);
    }

    /**
     * Check if step has buttons
     *
     * @param Step $step
     * @return bool
     */
    protected function stepHasButtons(Step $step): bool
    {
        return $step->component &&
               $step->component->buttons &&
               !empty($step->component->buttons);
    }


}
