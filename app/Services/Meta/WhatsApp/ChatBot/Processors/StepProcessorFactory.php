<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Processors;

use App\Domains\ChatBot\Step;
use App\Enums\StepType;
use App\Services\Meta\WhatsApp\ChatBot\Services\DynamicInputService;
use App\Services\Meta\WhatsApp\ChatBot\Services\FlowNavigationService;
use App\Services\Meta\WhatsApp\ChatBot\UseCases\Commands\ExecuteCommand;
use App\Services\VariableSubstitution\VariableSubstitutionService;

/**
 * Factory for creating step processors
 *
 * This factory is responsible for instantiating the appropriate processor
 * for each step type, managing dependencies, and providing a clean interface
 * for the ProcessFlowStep UseCase to get the right processor.
 *
 * @package App\Services\Meta\WhatsApp\ChatBot\Processors
 */
class StepProcessorFactory
{
    protected array $processors = [];
    protected DynamicInputService $dynamicInputService;
    protected ExecuteCommand $executeCommand;
    protected VariableSubstitutionService $variableSubstitutionService;
    protected FlowNavigationService $flowNavigationService;

    public function __construct(
        DynamicInputService $dynamicInputService,
        ExecuteCommand $executeCommand,
        VariableSubstitutionService $variableSubstitutionService,
        FlowNavigationService $flowNavigationService
    ) {
        $this->dynamicInputService = $dynamicInputService;
        $this->executeCommand = $executeCommand;
        $this->variableSubstitutionService = $variableSubstitutionService;
        $this->flowNavigationService = $flowNavigationService;
        $this->initializeProcessors();
    }

    /**
     * Get the appropriate processor for a step
     *
     * @param Step $step
     * @return StepProcessorInterface
     * @throws \InvalidArgumentException
     */
    public function getProcessor(Step $step): StepProcessorInterface
    {
        // Ensure step_type is set from legacy fields if needed
        $step->setStepTypeFromLegacyFields();

        $stepType = $step->step_type;

        if (!$stepType) {
            throw new \InvalidArgumentException("Step {$step->id} has no step_type defined");
        }

        $stepTypeValue = $stepType->value;

        if (!isset($this->processors[$stepTypeValue])) {
            throw new \InvalidArgumentException("No processor found for step type: {$stepTypeValue}");
        }

        return $this->processors[$stepTypeValue];
    }

    /**
     * Get processor by step type string
     *
     * @param string $stepType
     * @return StepProcessorInterface
     * @throws \InvalidArgumentException
     */
    public function getProcessorByType(string $stepType): StepProcessorInterface
    {
        if (!isset($this->processors[$stepType])) {
            throw new \InvalidArgumentException("No processor found for step type: {$stepType}");
        }

        return $this->processors[$stepType];
    }

    /**
     * Get all available processors
     *
     * @return array
     */
    public function getAllProcessors(): array
    {
        return $this->processors;
    }

    /**
     * Get all supported step types
     *
     * @return array
     */
    public function getSupportedStepTypes(): array
    {
        return array_keys($this->processors);
    }

    /**
     * Check if a step type is supported
     *
     * @param string $stepType
     * @return bool
     */
    public function isStepTypeSupported(string $stepType): bool
    {
        return isset($this->processors[$stepType]);
    }

    /**
     * Initialize all processors with their dependencies
     */
    protected function initializeProcessors(): void
    {
        $this->processors = [
            StepType::MESSAGE->value => new MessageStepProcessor($this->variableSubstitutionService),
            StepType::INTERACTIVE->value => new InteractiveStepProcessor(),
            StepType::INPUT->value => new InputStepProcessor($this->dynamicInputService, $this->variableSubstitutionService),
            StepType::COMMAND->value => new CommandStepProcessor($this->executeCommand),
            StepType::CONDITION->value => new ConditionStepProcessor($this->variableSubstitutionService),
            StepType::WEBHOOK->value => new WebhookStepProcessor($this->variableSubstitutionService),
            StepType::DELAY->value => new DelayStepProcessor(),
        ];
    }

    /**
     * Register a custom processor for a step type
     *
     * This allows for extending the factory with custom processors
     * without modifying the core factory code.
     *
     * @param string $stepType
     * @param StepProcessorInterface $processor
     */
    public function registerProcessor(string $stepType, StepProcessorInterface $processor): void
    {
        $this->processors[$stepType] = $processor;
    }

    /**
     * Unregister a processor for a step type
     *
     * @param string $stepType
     */
    public function unregisterProcessor(string $stepType): void
    {
        unset($this->processors[$stepType]);
    }
}
