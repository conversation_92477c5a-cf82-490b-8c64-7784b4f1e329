<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Services;

use App\Services\Meta\WhatsApp\ChatBot\Domains\WhatsAppInteraction;
use App\Services\Meta\WhatsApp\ChatBot\Services\FlowNavigationService;
use App\Domains\ChatBot\Step;

class ConditionalNavigationService
{
    protected FlowNavigationService $flowNavigationService;

    public function __construct(FlowNavigationService $flowNavigationService)
    {
        $this->flowNavigationService = $flowNavigationService;
    }

    /**
     * Determine next step based on user interaction
     *
     * FIXED: Use FlowNavigationService which already handles everything correctly
     *
     * @param Step $currentStep
     * @param WhatsAppInteraction $interaction
     * @return Step|null
     */
    public function getNextStepForInteraction(Step $currentStep, WhatsAppInteraction $interaction): ?Step
    {
        // Get user input from interaction
        $userInput = $this->getUserInputFromInteraction($interaction);

        // Build context for navigation
        $context = [
            'interaction' => $interaction,
            'button_id' => $interaction->getButtonId(),
            'list_selection_id' => $interaction->getListSelectionId(),
        ];

        // Use FlowNavigationService which already handles navigation_rules correctly
        return $this->flowNavigationService->getNextStep($currentStep, $userInput, $context);
    }

    /**
     * Extract user input from interaction
     *
     * @param WhatsAppInteraction $interaction
     * @return string
     */
    protected function getUserInputFromInteraction(WhatsAppInteraction $interaction): string
    {
        if ($interaction->isButtonInteraction()) {
            return $interaction->getButtonId() ?? '';
        }

        if ($interaction->isListInteraction()) {
            return $interaction->getListSelectionId() ?? '';
        }

        return $interaction->whatsapp_message_text ?? '';
    }

    /**
     * Check if step has conditional navigation
     *
     * FIXED: Use FlowNavigationService to check properly
     *
     * @param Step $step
     * @return bool
     */
    public function hasConditionalNavigation(Step $step): bool
    {
        // Check navigation_rules first (for pizzaria flow)
        if ($step->navigation_rules && is_array($step->navigation_rules) && !empty($step->navigation_rules)) {
            return true;
        }

        // Check if step has buttons with navigation (legacy)
        if ($step->component && $step->component->buttons && !empty($step->component->buttons)) {
            foreach ($step->component->buttons as $button) {
                if ($button->isConditionalButton() || $button->isNavigationButton()) {
                    return true;
                }
            }
        }

        return false;
    }
}
