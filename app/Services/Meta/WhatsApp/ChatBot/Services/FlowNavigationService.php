<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Services;

use App\Domains\ChatBot\Step;
use App\Domains\ChatBot\StepNavigation;
use App\Repositories\StepRepository;
use App\Repositories\StepNavigationRepository;
use App\Helpers\DBLog;
use Exception;

/**
 * Unified navigation service for ChatBot flows
 *
 * This service centralizes all navigation logic, replacing the scattered
 * navigation code in processors and services. It handles:
 * - Button-based navigation
 * - Text-based navigation with rules
 * - Conditional navigation
 * - Position to step_id conversion
 */
class FlowNavigationService
{
    protected StepRepository $stepRepository;
    protected StepNavigationRepository $stepNavigationRepository;

    public function __construct(
        StepRepository $stepRepository,
        StepNavigationRepository $stepNavigationRepository
    ) {
        $this->stepRepository = $stepRepository;
        $this->stepNavigationRepository = $stepNavigationRepository;
    }

    /**
     * Determine next step based on user interaction
     *
     * This is the main entry point for navigation logic.
     * It processes user input and returns the next step to navigate to.
     *
     * @param Step $currentStep Current step in the flow
     * @param string $userInput User's input (button text, message text, etc.)
     * @param array $context Additional context (conversation, interaction, etc.)
     * @return Step|null Next step to navigate to, or null if flow should end
     */
    public function getNextStep(Step $currentStep, string $userInput, array $context = []): ?Step
    {
        try {
            // 1. Check for navigation rules first (highest priority)
            $nextStepFromRules = $this->getNextStepFromNavigationRules($currentStep, $userInput, $context);
            if ($nextStepFromRules) {
                return $nextStepFromRules;
            }

            // 2. Check for button-based navigation
            $nextStepFromButton = $this->getNextStepFromButtonNavigation($currentStep, $userInput, $context);
            if ($nextStepFromButton) {
                return $nextStepFromButton;
            }

            // 3. Fall back to default next step (convert position to step_id)
            $defaultNextStep = $this->getDefaultNextStep($currentStep);
            if ($defaultNextStep) {
                return $defaultNextStep;
            }

            return null;

        } catch (Exception $e) {
            // On error, try to return default next step as fallback
            return $this->getDefaultNextStep($currentStep);
        }
    }

    /**
     * Get next step based on navigation rules (JSON field or StepNavigation entities)
     *
     * FIXED: Check navigation_rules JSON first (for pizzaria flow), then StepNavigation entities
     *
     * @param Step $currentStep
     * @param string $userInput
     * @param array $context
     * @return Step|null
     */
    protected function getNextStepFromNavigationRules(Step $currentStep, string $userInput, array $context = []): ?Step
    {
        // 1. First check navigation_rules JSON field (for pizzaria flow)
        $nextStepFromJson = $this->getNextStepFromJsonNavigationRules($currentStep, $userInput, $context);
        if ($nextStepFromJson) {
            return $nextStepFromJson;
        }

        // 2. Fallback to StepNavigation entities (legacy system)
        $matchingRule = $currentStep->findMatchingNavigationRule($userInput, $context);
        if (!$matchingRule) {
            return null;
        }

        // Resolve the target step identifier
        $targetStepId = $this->resolveStepIdentifier($matchingRule->target_step_identifier, $currentStep);
        if (!$targetStepId) {
            return null;
        }

        return $this->stepRepository->fetchById($targetStepId);
    }

    /**
     * Get next step from JSON navigation_rules field (for pizzaria flow)
     *
     * This processes navigation_rules like:
     * [
     *   {
     *     "condition": "button_clicked",
     *     "button_data": "small",
     *     "target_step_identifier": "choose_pizza_flavor",
     *     "variables": {"order.pizza_size": "Pequena", "order.total": 25.00}
     *   }
     * ]
     *
     * @param Step $currentStep
     * @param string $userInput
     * @param array $context
     * @return Step|null
     */
    protected function getNextStepFromJsonNavigationRules(Step $currentStep, string $userInput, array $context = []): ?Step
    {
        if (!$currentStep->navigation_rules || !is_array($currentStep->navigation_rules)) {
            return null;
        }

        $buttonId = $context['button_id'] ?? null;

        foreach ($currentStep->navigation_rules as $rule) {
            if ($this->jsonRuleMatchesInput($rule, $userInput, $buttonId, $context)) {
                $targetStepIdentifier = $rule['target_step_identifier'] ?? null;
                if ($targetStepIdentifier) {
                    $targetStepId = $this->resolveStepIdentifier($targetStepIdentifier, $currentStep);
                    if ($targetStepId) {
                        return $this->stepRepository->fetchById($targetStepId);
                    }
                }
            }
        }

        return null;
    }

    /**
     * Check if JSON navigation rule matches user input
     *
     * @param array $rule
     * @param string $userInput
     * @param string|null $buttonId
     * @param array $context
     * @return bool
     */
    protected function jsonRuleMatchesInput(array $rule, string $userInput, ?string $buttonId, array $context = []): bool
    {
        $condition = $rule['condition'] ?? '';

        switch ($condition) {
            case 'button_clicked':
                $buttonData = $rule['button_data'] ?? '';
                // Match against button ID or button data
                return $buttonId === $buttonData || $userInput === $buttonData;

            case 'text_equals':
                $expectedText = $rule['text'] ?? '';
                return strcasecmp($userInput, $expectedText) === 0;

            case 'text_contains':
                $expectedText = $rule['text'] ?? '';
                return stripos($userInput, $expectedText) !== false;

            default:
                return false;
        }
    }

    /**
     * Get next step based on button navigation
     *
     * @param Step $currentStep
     * @param string $userInput
     * @param array $context
     * @return Step|null
     */
    protected function getNextStepFromButtonNavigation(Step $currentStep, string $userInput, array $context = []): ?Step
    {
        // Check if current step has buttons
        if (!$currentStep->component || !$currentStep->component->buttons) {
            return null;
        }

        // Look for button that matches user input
        foreach ($currentStep->component->buttons as $button) {
            if ($this->buttonMatchesInput($button, $userInput)) {
                // Check if button has conditional navigation
                if ($button->internal_type === 'condition') {
                    // Handle conditional button navigation
                    return $this->handleConditionalButtonNavigation($button, $currentStep, $context);
                }

                // For regular buttons, use default navigation
                break;
            }
        }

        return null;
    }

    /**
     * Get default next step (converts position to step_id)
     *
     * @param Step $currentStep
     * @return Step|null
     */
    protected function getDefaultNextStep(Step $currentStep): ?Step
    {
        if ($currentStep->next_step === null) {
            return null;
        }

        // Convert position to step_id
        return $this->convertPositionToStepId($currentStep->next_step, $currentStep->flow_id);
    }

    /**
     * Convert position to step_id within the same flow
     *
     * @param int $position
     * @param int $flowId
     * @return Step|null
     */
    public function convertPositionToStepId(int $position, int $flowId): ?Step
    {
        return $this->stepRepository->findByPositionInFlow($position, $flowId);
    }

    /**
     * Resolve step identifier to actual step ID
     *
     * @param string $identifier
     * @param Step $currentStep
     * @return int|null
     */
    protected function resolveStepIdentifier(string $identifier, Step $currentStep): ?int
    {
        // Handle special identifiers
        switch ($identifier) {
            case 'next_step':
                if ($currentStep->next_step !== null) {
                    $nextStep = $this->convertPositionToStepId($currentStep->next_step, $currentStep->flow_id);
                    return $nextStep?->id;
                }
                return null;

            case 'previous_step':
            case 'earlier_step':
                if ($currentStep->earlier_step !== null) {
                    $previousStep = $this->convertPositionToStepId($currentStep->earlier_step, $currentStep->flow_id);
                    return $previousStep?->id;
                }
                return null;

            case 'restart_flow':
                $initialStep = $this->stepRepository->getInitialStepForFlow($currentStep->flow_id);
                return $initialStep?->id;

            default:
                // Try to parse as step identifier within flow
                $step = $this->stepRepository->findByIdentifierInFlow($identifier, $currentStep->flow_id);
                if ($step) {
                    return $step->id;
                }

                // Try to parse as direct step ID
                if (is_numeric($identifier)) {
                    return (int) $identifier;
                }

                return null;
        }
    }

    /**
     * Check if button matches user input
     *
     * @param mixed $button
     * @param string $userInput
     * @return bool
     */
    protected function buttonMatchesInput($button, string $userInput): bool
    {
        if (!$button) {
            return false;
        }

        // Match by button text (case-insensitive)
        if (isset($button->text) && strcasecmp($button->text, $userInput) === 0) {
            return true;
        }

        // Match by button ID
        if (isset($button->id) && $userInput === (string) $button->id) {
            return true;
        }

        return false;
    }

    /**
     * Handle conditional button navigation
     *
     * @param mixed $button
     * @param Step $currentStep
     * @param array $context
     * @return Step|null
     */
    protected function handleConditionalButtonNavigation($button, Step $currentStep, array $context = []): ?Step
    {
        // This would implement conditional logic based on button's internal_data
        // For now, return default next step
        return $this->getDefaultNextStep($currentStep);
    }

    /**
     * Check if step has any navigation rules
     *
     * @param Step $step
     * @return bool
     */
    public function hasNavigationRules(Step $step): bool
    {
        return !empty($step->getActiveNavigationRules());
    }

    /**
     * Check if step has conditional navigation
     *
     * @param Step $step
     * @return bool
     */
    public function hasConditionalNavigation(Step $step): bool
    {
        return $this->hasNavigationRules($step) || $this->hasConditionalButtons($step);
    }

    /**
     * Check if step has conditional buttons
     *
     * @param Step $step
     * @return bool
     */
    protected function hasConditionalButtons(Step $step): bool
    {
        if (!$step->component || !$step->component->buttons) {
            return false;
        }

        foreach ($step->component->buttons as $button) {
            if ($button->internal_type === 'condition') {
                return true;
            }
        }

        return false;
    }
}
