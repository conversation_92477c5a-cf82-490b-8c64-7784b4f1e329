<?php

namespace App\Services\Meta\WhatsApp\UseCases\Webhook;

use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Organization;
use App\Domains\WhatsApp\ChangeValue;
use App\Enums\MessageStatus;
use App\Repositories\MessageRepository;
use App\Services\Meta\WhatsApp\Repositories\WhatsAppMessageRepository;
use App\Services\Meta\WhatsApp\UseCases\WhatsAppMessage\FetchByWAMID;
use App\Services\Meta\WhatsApp\UseCases\WhatsAppWebhookEntry\ProcessWebhookEntry;
use App\UseCases\ChatBot\ExchangedMessage\SaveOutboundFromStatusUpdate;
use App\UseCases\ChatBot\Client\UpdateWhatsAppFromStatusUpdate;

class ProcessWebhookMessageStatusUpdate
{
    private ProcessWebhookEntry $processWebhookEntry;
    private FetchByWAMID $fetchByWAMID;
    private WhatsAppMessageRepository $whatsAppMessageRepository;
    private MessageRepository $messageRepository;
    private SaveOutboundFromStatusUpdate $saveOutboundFromStatusUpdate;
    private UpdateWhatsAppFromStatusUpdate $updateWhatsAppFromStatusUpdate;

    public function __construct(
        ProcessWebhookEntry $processWebhookEntry,
        FetchByWAMID $fetchByWAMID,
        WhatsAppMessageRepository $whatsAppMessageRepository,
        MessageRepository $messageRepository,
        SaveOutboundFromStatusUpdate $saveOutboundFromStatusUpdate,
        UpdateWhatsAppFromStatusUpdate $updateWhatsAppFromStatusUpdate
    ) {
        $this->processWebhookEntry = $processWebhookEntry;
        $this->fetchByWAMID = $fetchByWAMID;
        $this->whatsAppMessageRepository = $whatsAppMessageRepository;
        $this->messageRepository = $messageRepository;
        $this->saveOutboundFromStatusUpdate = $saveOutboundFromStatusUpdate;
        $this->updateWhatsAppFromStatusUpdate = $updateWhatsAppFromStatusUpdate;
    }

    /**
     * Process webhook status updates
     *
     * @param ChangeValue $changeValue
     * @param Organization $organization
     * @param PhoneNumber $phoneNumber
     * @return array
     */
    public function perform(ChangeValue $changeValue, Organization $organization, PhoneNumber $phoneNumber): array
    {
        try {
            if (!$changeValue->hasStatuses()) {
                return [
                    'success' => false,
                    'error' => 'No statuses found in change value',
                    'processed' => 0
                ];
            }

            $whatsAppMessage = $this->fetchByWAMID->perform($changeValue);

            $messageUpdated = false;
            $whatsAppMessageUpdated = false;

            if ($whatsAppMessage && $whatsAppMessage->message) {
                $latestStatus = $changeValue->getLatestStatus();

                if ($latestStatus) {
                    $messageStatus = $latestStatus['status'] ?? null;

                    if ($messageStatus) {
                        $whatsAppMessage->message->updateMessageStatus($messageStatus);
                        $this->messageRepository->update($whatsAppMessage->message, $organization->id);
                        $messageUpdated = true;

                        $whatsAppMessage->updateWhatsAppMessageStatus($messageStatus);
                        $this->whatsAppMessageRepository->update($whatsAppMessage);
                        $whatsAppMessageUpdated = true;

                        // Save ExchangedMessage for delivered status
                        if ($messageStatus === 'delivered') {
                            $this->saveOutboundFromStatusUpdate->perform(
                                $latestStatus,
                                $organization,
                                $phoneNumber,
                                $whatsAppMessage->message
                            );
                        }

                        // Save Client From here:
                        $this->updateWhatsAppFromStatusUpdate->perform(
                            $latestStatus,
                            $organization,
                            $whatsAppMessage->message
                        );
                    }
                }
            }

            // Prepare webhook data for ProcessWebhookEntry
            $webhookData = [
                'object' => 'whatsapp_business_account',
                'entry' => [
                    [
                        'id' => $phoneNumber->whatsapp_business_id ?? 'unknown',
                        'changes' => [
                            [
                                'field' => 'messages',
                                'value' => $changeValue->toArray()
                            ]
                        ]
                    ]
                ]
            ];

            // Process through existing ProcessWebhookEntry service
            $result = $this->processWebhookEntry->perform($webhookData);

            $processedCount = count($changeValue->statuses);

            return [
                'success' => $result['success'] ?? false,
                'type' => 'status',
                'processed' => $processedCount,
                'organization_id' => $organization->id,
                'phone_number_id' => $phoneNumber->whatsapp_phone_number_id,
                'processed_entries' => $result['processed_entries'] ?? 0,
                'entries' => $result['entries'] ?? [],
                'statuses_processed' => $changeValue->extractStatusSummary(),
                'message_updated' => $messageUpdated,
                'whatsapp_message_updated' => $whatsAppMessageUpdated,
                'wam_id' => $changeValue->getPrimaryWamId()
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'type' => 'status',
                'processed' => 0,
                'organization_id' => $organization->id,
                'phone_number_id' => $phoneNumber->whatsapp_phone_number_id
            ];
        }
    }


}
